<?php

date_default_timezone_set('Europe/London');
chdir('../');
require('includes/application_top.php');

/**
 * Monthly Discount Campaign for CG+ Members
 * Sends personalized discount codes to Bronze, Silver, and Gold tier members
 */
class MonthlyDiscountCampaign {

    private $current_month;
    private $current_year;
    private $last_day;

    public function __construct() {
        $this->current_month = strtoupper(date("M"));
        $this->current_year = date("Y");
        $this->last_day = date("t");
    }

    public function run() {
        if (!$this->isFirstDayOfMonth()) {
            echo "Not the 1st of the month. Exiting.\n";
            return;
        }

        //echo "Starting CG+ Monthly Discount Campaign for {$this->current_month} {$this->current_year}\n";

        foreach ($this->getDiscountTiers() as $tier_level => $tier_info) {
            $this->processTier($tier_level, $tier_info);
        }

        echo "CG+ Monthly Discount Campaign completed.\n";
    }

    public function test() {
        //echo "Starting CG+ Monthly Discount Campaign TEST MODE\n";
        // echo "Current Month: {$this->current_month} {$this->current_year}\n\n";

        // Send test email for each tier
        foreach ($this->getDiscountTiers() as $tier_level => $tier_info) {
            $this->sendTestEmail($tier_level, $tier_info);
        }
    }

    private function sendTestEmail(int $tier_level, array $tier_info): void {
        //echo "Sending test email for {$tier_info['name']} tier...\n";

        $discount_code = $this->generateDiscountCode($tier_info['code_suffix']);

        // Create test member data
        $test_member = [
            'customers_id' => 999999, // Test ID
            'customers_email_address' => '<EMAIL>',
            'customers_firstname' => 'Test',
            'customers_lastname' => 'User',
            'customers_cgars_plus_active' => $tier_level
        ];

        if ($this->sendEmailToMember($test_member, $tier_info, $discount_code)) {
            echo "✓ TEST EMAIL SENT SUCCESSFULLY\n";
        } else {
            echo "✗ TEST EMAIL FAILED\n";
        }

        echo "\n";
    }

    private function isFirstDayOfMonth(): bool {
        return date("j") == 1;
    }

    private function processTier(int $tier_level, array $tier_info): void {
        echo "Processing {$tier_info['name']} members...\n";

        $discount_code = $this->generateDiscountCode($tier_info['code_suffix']);

        if (!$this->createDiscountCodeIfNeeded($discount_code, $tier_info, $tier_level)) {
            return;
        }

        $this->sendEmailsToTierMembers($tier_level, $tier_info, $discount_code);
    }

    private function generateDiscountCode(string $suffix): string {
        return $this->current_month . $suffix;
    }

    private function createDiscountCodeIfNeeded(string $discount_code, array $tier_info, int $tier_level): bool {
        if ($this->discountCodeExists($discount_code)) {
            echo "Discount code {$discount_code} already exists for {$tier_info['name']} tier.\n";
            echo "Continuing with email sending...\n";
            return true;
        }

        echo "Creating new discount code {$discount_code} for {$tier_info['name']} tier.\n";

        $sql = $this->buildDiscountCodeInsertSQL($discount_code, $tier_info, $tier_level);
        $result = tep_db_query($sql);

        if (!$result) {
            echo "Error creating discount code {$discount_code}\n";
            return false;
        }

        echo "Created discount code {$discount_code} for {$tier_info['name']} tier.\n";
        return true;
    }

    private function discountCodeExists(string $discount_code): bool {
        $query = tep_db_query("
            SELECT coupons_id 
            FROM " . TABLE_DISCOUNT_COUPONS . " 
            WHERE coupons_id = '" . tep_db_input($discount_code) . "'
        ");
        return tep_db_num_rows($query) > 0;
    }

    private function buildDiscountCodeInsertSQL(string $discount_code, array $tier_info, int $tier_level): string {
        return "
            INSERT INTO " . TABLE_DISCOUNT_COUPONS . " (
                coupons_id, 
                coupons_description,
                coupons_discount_type, 
                coupons_discount_amount, 
                coupons_min_order, 
                coupons_min_order_type,
                coupons_date_start,
                coupons_date_end,
                coupons_number_available,
                coupons_max_use,
                status,
                cgars_plus,
                cgars_plus_tier
            ) VALUES (
                '" . tep_db_input($discount_code) . "',
                'CG+ {$tier_info['name']} Monthly Discount - {$this->current_month} {$this->current_year}',
                'fixed',
                '" . (float)$tier_info['discount_amount'] . "',
                '" . (float)$tier_info['min_order'] . "',
                'price',
                '" . date('Y-m-d 00:00:00') . "',
                '" . date('Y-m-d 23:59:59', strtotime('last day of this month')) . "',
                0,
                1,
                1,
                1,
                '" . $tier_level . "'
            )
        ";
    }

    private function sendEmailsToTierMembers(int $tier_level, array $tier_info, string $discount_code): void {
        $members = $this->getTierMembers($tier_level);
        $member_count = count($members);
        $email_sent_count = 0;

        echo "Found {$member_count} {$tier_info['name']} members to process.\n";

        foreach ($members as $member) {
            if ($this->hasAlreadyReceivedEmail($member['customers_id'], $tier_level)) {
                continue;
            }

            if ($this->sendEmailToMember($member, $tier_info, $discount_code)) {
                $this->recordEmailSent($member['customers_id'], $tier_level, $discount_code);
                $email_sent_count++;
                echo "Sent discount email to {$member['customers_email_address']} ({$member['customers_firstname']} {$member['customers_lastname']})\n";
            } else {
                echo "Failed to send email to {$member['customers_email_address']}\n";
            }

            usleep(100000); // 0.1 second delay
        }

        echo "Processed {$member_count} {$tier_info['name']} members, sent {$email_sent_count} emails.\n";
    }

    private function getTierMembers(int $tier_level): array {
        $query = tep_db_query("
            SELECT 
                customers_id,
                customers_email_address,
                customers_firstname,
                customers_lastname,
                customers_cgars_plus_active
            FROM " . TABLE_CUSTOMERS . "
            WHERE customers_cgars_plus_active = '" . (int)$tier_level . "'
            ORDER BY customers_id
        ");

        $members = [];
        while ($row = tep_db_fetch_array($query)) {
            $members[] = $row;
        }

        return $members;
    }

    private function hasAlreadyReceivedEmail(int $customer_id, int $tier_level): bool {
        $query = tep_db_query("
            SELECT id 
            FROM cgars_plus_monthly_discounts_sent 
            WHERE customers_id = '" . (int)$customer_id . "' 
            AND discount_month = '" . tep_db_input($this->current_month . $this->current_year) . "'
            AND tier_level = '" . (int)$tier_level . "'
        ");
        return tep_db_num_rows($query) > 0;
    }

    private function sendEmailToMember(array $member, array $tier_info, string $discount_code): bool {
        return $this->sendMonthlyDiscountEmail($member, $tier_info, $discount_code);
    }

    private function recordEmailSent(int $customer_id, int $tier_level, string $discount_code): void {
        tep_db_query("
            INSERT INTO cgars_plus_monthly_discounts_sent (
                customers_id, 
                discount_month, 
                tier_level, 
                discount_code, 
                date_sent
            ) VALUES (
                '" . (int)$customer_id . "',
                '" . tep_db_input($this->current_month . $this->current_year) . "',
                '" . (int)$tier_level . "',
                '" . tep_db_input($discount_code) . "',
                NOW()
            )
        ");
    }

    private function sendMonthlyDiscountEmail(array $member, array $tier_info, string $discount_code): bool {
        $subject = "Your {$tier_info['name']} discount is here! Use code {$discount_code}";

        $discount_formatted = '&pound;' . number_format($tier_info['discount_amount'], 2);
        $min_order_formatted = '&pound;' . number_format($tier_info['min_order'], 2);

        $month_names = [
            'JAN' => 'January',
            'FEB' => 'February',
            'MAR' => 'March',
            'APR' => 'April',
            'MAY' => 'May',
            'JUN' => 'June',
            'JUL' => 'July',
            'AUG' => 'August',
            'SEP' => 'September',
            'OCT' => 'October',
            'NOV' => 'November',
            'DEC' => 'December'
        ];
        $month_name = $month_names[$this->current_month] ?? $this->current_month;

        $email_content = $this->buildEmailContent($tier_info, $discount_code, $discount_formatted, $min_order_formatted, $month_name);

        return true;
        // try {
        //     tep_send_default_email(
        //         $member['customers_email_address'],
        //         $member['customers_firstname'] . ' ' . $member['customers_lastname'],
        //         $subject,
        //         'Your ' . $tier_info['name'] . ' discount is here!',
        //         'Use code ' . $discount_code,
        //         $email_content
        //     );
        //     return true;
        // } catch (Exception $e) {
        //     error_log("Failed to send monthly discount email to {$member['customers_email_address']}: " . $e->getMessage());
        //     return false;
        // }
    }

    private function buildEmailContent(array $tier_info, string $discount_code, string $discount_formatted, string $min_order_formatted, string $month_name): string {
        return '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;"><div style="background-color: #f9f9f9; padding: 20px; border-radius: 10px; margin-bottom: 20px;"><p style="font-size: 16px; line-height: 1.6; margin: 0 0 15px 0;"><strong>' . $tier_info['name'] . ' discount</strong> ' . $discount_formatted . ' off when you spend ' . $min_order_formatted . ' or more on any basket.</p><div style="background-color: #3e484d; color: white; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;"><h3 style="margin: 0 0 10px 0; font-size: 18px;">Your Exclusive Discount Code</h3><div style="font-size: 24px; font-weight: bold; letter-spacing: 2px; background-color: white; color: #3e484d; padding: 10px; border-radius: 5px; display: inline-block;">' . $discount_code . '</div></div><p style="font-size: 14px; color: #666; margin: 0; line-height: 1.4;"><strong>T&C\'s apply.</strong> ' . $tier_info['name'] . ' membership level only.<br>One use per customer. Valid from 1st ' . $month_name . ' ' . $this->current_year . ' until midnight ' . $this->last_day . ' ' . $month_name . ' ' . $this->current_year . '.</p></div><div style="text-align: center; margin-top: 30px;"><a href="https://www.cgarsltd.co.uk" style="background-color: #9E8B7B; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Shop Now</a></div><div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; font-size: 12px; color: #999;"><p>Thank you for being a valued ' . $tier_info['name'] . ' member of C.Gars Plus!</p><p>If you have any questions, please contact us at <a href="mailto:<EMAIL>" style="color: #9E8B7B;"><EMAIL></a></p></div></div>';
    }


    private function getDiscountTiers(): array {
        return get_all_tiers();
    }
}

// Execute campaign
$campaign = new MonthlyDiscountCampaign();
$campaign->run();
